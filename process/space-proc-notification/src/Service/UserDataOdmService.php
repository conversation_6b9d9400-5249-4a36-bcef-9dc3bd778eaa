<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\UserData;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;

/**
 * UserDataOdmService handles user data operations using MongoDB ODM
 * Following the established pattern from space-proc-me microservice
 */
class UserDataOdmService
{
    use LoggerTrait;

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Get user by userId
     */
    public function getUserByUserId(string $userId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user by userId', [
                'userId' => $userId,
            ]);

            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User not found', [
                    'userId' => $userId,
                ]);

                return new WSResponse(Response::HTTP_OK, json_encode(['documents' => []]));
            }

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User found successfully', [
                'userId' => $userId,
                'userDbId' => $userData->getUserDbId(),
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode([
                'documents' => [$userData->toArray()]
            ]));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user by userId', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode([
                    'error' => ['message' => 'Database service temporarily unavailable']
                ]));
            }

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode([
                'error' => ['message' => $e->getMessage()]
            ]));
        }
    }

    /**
     * Get users by session ID with aggregation pipeline
     * This method replicates the MongoDB Atlas aggregation functionality
     */
    public function getUsersBySessionId(string $userId, string $vin, string $sessionId): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting users by session ID', [
                'userId' => $userId,
                'vin' => $vin,
                'sessionId' => $sessionId,
            ]);

            // Use DocumentManager for complex aggregation
            $dm = $this->mongoDBService->getDocumentManager();
            $qb = $dm->createQueryBuilder(UserData::class);

            // Match user and vehicle
            $qb->field('userId')->equals($userId)
               ->field('vehicle.vin')->equals($vin);

            $userData = $qb->getQuery()->getSingleResult();

            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' No user data found', [
                    'userId' => $userId,
                    'vin' => $vin,
                ]);

                return new WSResponse(Response::HTTP_OK, json_encode(['documents' => []]));
            }

            // Filter push data by session ID
            $filteredPushData = [];
            foreach ($userData->getPush() as $pushData) {
                if ($pushData->getCommandSession() && 
                    $pushData->getCommandSession()->getRemoteSessionId() === $sessionId) {
                    $filteredPushData[] = $pushData->toArray();
                }
            }

            $result = [
                '_id' => 'pushDetails',
                'push' => $filteredPushData
            ];

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Users by session ID retrieved successfully', [
                'userId' => $userId,
                'vin' => $vin,
                'sessionId' => $sessionId,
                'push_count' => count($filteredPushData),
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode(['documents' => [$result]]));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting users by session ID', [
                'userId' => $userId,
                'vin' => $vin,
                'sessionId' => $sessionId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode([
                    'error' => ['message' => 'Database service temporarily unavailable']
                ]));
            }

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode([
                'error' => ['message' => $e->getMessage()]
            ]));
        }
    }

    /**
     * Get user data by userId for registration purposes
     * Returns the userDbId if user exists
     */
    public function getUserData(string $userId): string
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user data for registration', [
                'userId' => $userId,
            ]);

            $userData = $this->mongoDBService->findOneBy(UserData::class, ['userId' => $userId]);

            if (!$userData) {
                $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User data not found', [
                    'userId' => $userId,
                ]);
                return '';
            }

            $userDbId = $userData->getUserDbId() ?? '';

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User data retrieved successfully', [
                'userId' => $userId,
                'userDbId' => $userDbId,
            ]);

            return $userDbId;
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user data', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            return '';
        }
    }

    /**
     * Check if the exception is a MongoDB connection error
     */
    private function isMongoConnectionError(\Exception $e): bool
    {
        return str_contains($e->getMessage(), 'Connection') ||
               str_contains($e->getMessage(), 'connection') ||
               str_contains($e->getMessage(), 'timeout') ||
               str_contains($e->getMessage(), 'network');
    }
}
