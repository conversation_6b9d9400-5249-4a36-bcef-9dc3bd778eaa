<?php

namespace App\Service;

use App\Helper\WSResponse;
use App\Trait\LoggerTrait;
use Space\MongoDocuments\Document\Notification;
use Space\MongoDocuments\Document\NotificationMessage;
use Space\MongoDocuments\Document\NotificationData;
use Space\MongoDocuments\Document\NotificationContent;
use Space\MongoDocuments\Document\NotificationContext;
use Space\MongoDocuments\Document\NotificationInApp;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Uid\Uuid;

/**
 * NotificationOdmService handles notification operations using MongoDB ODM
 * Following the established pattern from space-proc-me microservice
 */
class NotificationOdmService
{
    use LoggerTrait;

    public const NOTIFICATION_TYPE_USER = 'user';
    public const NOTIFICATION_TYPE_VEHICLE = 'vehicle';

    public function __construct(
        private MongoDBService $mongoDBService
    ) {
    }

    /**
     * Get vehicle notifications for a specific user and VIN
     */
    public function getVehicleNotifications(string $userId, string $vin, array $data): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting vehicle notifications', [
                'userId' => $userId,
                'vin' => $vin,
                'filters' => $data,
            ]);

            $repository = $this->mongoDBService->getDocumentManager()->getRepository(Notification::class);
            $result = $repository->getVehicleNotifications($userId, $vin, $data);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Vehicle notifications retrieved successfully', [
                'userId' => $userId,
                'vin' => $vin,
                'count' => $result['count'] ?? 0,
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode(['documents' => [$result]]));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting vehicle notifications', [
                'userId' => $userId,
                'vin' => $vin,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode([
                    'error' => ['message' => 'Database service temporarily unavailable']
                ]));
            }

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode([
                'error' => ['message' => $e->getMessage()]
            ]));
        }
    }

    /**
     * Get user vehicle notifications for all vehicles of a user
     */
    public function getUserVehicleNotifications(string $userId, array $data): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Getting user vehicle notifications', [
                'userId' => $userId,
                'filters' => $data,
            ]);

            $repository = $this->mongoDBService->getDocumentManager()->getRepository(Notification::class);
            $result = $repository->getUserVehicleNotifications($userId, $data);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' User vehicle notifications retrieved successfully', [
                'userId' => $userId,
                'vehicles_count' => count($result),
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode(['documents' => $result]));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error getting user vehicle notifications', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            // Check if it's a MongoDB connection error
            if ($this->isMongoConnectionError($e)) {
                return new WSResponse(Response::HTTP_SERVICE_UNAVAILABLE, json_encode([
                    'error' => ['message' => 'Database service temporarily unavailable']
                ]));
            }

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode([
                'error' => ['message' => $e->getMessage()]
            ]));
        }
    }

    /**
     * Save notification for a specific user
     */
    public function saveNotification(string $type, string $userId, array $message, ?string $vin = null): WSResponse
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Saving notification', [
                'type' => $type,
                'userId' => $userId,
                'vin' => $vin,
            ]);

            // Find or create notification document for user
            $notification = $this->mongoDBService->findOneBy(Notification::class, ['userId' => $userId]);
            if (!$notification) {
                $notification = new Notification();
                $notification->setUserId($userId);
            }

            // Create notification message
            $notificationMessage = $this->createNotificationMessage($type, $userId, $message, $vin);
            $notification->addMessage($notificationMessage);

            // Save the notification
            $this->mongoDBService->save($notification);

            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ' Notification saved successfully', [
                'userId' => $userId,
                'type' => $type,
                'vin' => $vin,
            ]);

            return new WSResponse(Response::HTTP_OK, json_encode([
                'matchedCount' => 1,
                'modifiedCount' => 1
            ]));
        } catch (\Exception $e) {
            $this->logger->error(__CLASS__ . '::' . __METHOD__ . ' Error saving notification', [
                'userId' => $userId,
                'type' => $type,
                'error' => $e->getMessage(),
                'error_type' => get_class($e),
            ]);

            return new WSResponse(Response::HTTP_INTERNAL_SERVER_ERROR, json_encode([
                'error' => ['message' => $e->getMessage()]
            ]));
        }
    }

    /**
     * Create a notification message from the provided data
     */
    private function createNotificationMessage(string $type, string $userId, array $message, ?string $vin = null): NotificationMessage
    {
        // Adding TTL for 45 days
        $expiresAt = strtotime("+45 days", time());

        // Format the notification data based on type
        $formattedMessage = [];
        if ($type === self::NOTIFICATION_TYPE_USER) {
            $formattedMessage = $this->userXfFormattedNotification($userId, $message);
        } elseif ($type === self::NOTIFICATION_TYPE_VEHICLE) {
            $formattedMessage = $this->vehicleXfFormattedNotification($userId, $message, $vin);
        }

        // Create notification data
        $notificationData = $this->createNotificationData($formattedMessage);

        // Create notification message
        $notificationMessage = new NotificationMessage();
        $notificationMessage->setNotificationData($notificationData);
        $notificationMessage->setCriticality(strtolower($message['criticality'] ?? ''));
        $notificationMessage->setType($type);
        $notificationMessage->setVin($vin);
        $notificationMessage->setExpiresAt($expiresAt);

        return $notificationMessage;
    }

    /**
     * Create NotificationData from formatted message array
     */
    private function createNotificationData(array $formattedMessage): NotificationData
    {
        $notificationData = new NotificationData();
        $notificationData->setId($formattedMessage['id'] ?? null);
        $notificationData->setEventName($formattedMessage['eventName'] ?? null);
        $notificationData->setTimestamp($formattedMessage['timestamp'] ?? null);
        $notificationData->setVin($formattedMessage['vin'] ?? null);

        // Create notification content
        if (isset($formattedMessage['notification'])) {
            $notificationContent = $this->createNotificationContent($formattedMessage['notification']);
            $notificationData->setNotification($notificationContent);
        }

        return $notificationData;
    }

    /**
     * Create NotificationContent from notification array
     */
    private function createNotificationContent(array $notification): NotificationContent
    {
        $notificationContent = new NotificationContent();
        $notificationContent->setData($notification['data'] ?? []);
        $notificationContent->setClickAction($notification['click_action'] ?? null);

        // Create context
        if (isset($notification['context'])) {
            $context = new NotificationContext();
            $context->setBrandMarketingName($notification['context']['brandMarketingName'] ?? null);
            $context->setFirstName($notification['context']['firstName'] ?? null);
            $context->setModel($notification['context']['model'] ?? null);
            $context->setNickname($notification['context']['nickname'] ?? null);
            $notificationContent->setContext($context);
        }

        // Create in-app data
        if (isset($notification['in-app'])) {
            $inApp = new NotificationInApp();
            $inApp->setCriticality($notification['in-app']['criticality'] ?? null);
            $inApp->setBody($notification['in-app']['body'] ?? null);
            $inApp->setSubtitle($notification['in-app']['subtitle'] ?? null);
            $inApp->setTitle($notification['in-app']['title'] ?? null);
            $notificationContent->setInApp($inApp);
        }

        return $notificationContent;
    }

    /**
     * Formats a user notification for XF format.
     *
     * @param string $userId  The unique identifier of the user.
     * @param array  $message The message data containing notification details.
     *
     * @return array The formatted notification data.
     */
    private function userXfFormattedNotification(string $userId, array $message): array
    {
        $notificationData = [];
        $notification = [
            "context" => [
                "brandMarketingName" => $message['data']['context']['brandMarketingName'] ?? "",
                "firstName" => $message['data']['context']['firstName'] ?? '',
                "model" => $message['data']['context']['model'] ?? "",
                "nickname" => $message['data']['context']['nickname'] ?? "",
            ],
            "in-app" => [
                "body" => $message['notification']['body'] ?? '',
                "criticality" => $message['criticality'],
                "subtitle" => $message['notification']['subtitle'] ?? '',
                "title" => $message['notification']['title'] ?? ''
            ],
            "data" => [
                "notificationId" => $message['data']['serviceData']['Data']['notificationId'] ?? '',
                "userId" => $userId,
            ],
            "click_action" => $message['apiPushConfig']['staticConfig']['android']['notification']['click_action'] ?? ""
        ];

        $notificationData['id'] = Uuid::v4();
        $notificationData['notification'] = $notification;
        $notificationData['timestamp'] = $message['data']['serviceData']['Timestamp'] ?? null;
        $notificationData['vin'] = $message['serviceData']["Data"]['eventName'] ?? "";

        return $notificationData;
    }

    /**
     * Formats a vehicle notification for XF format.
     *
     * @param string $userId  The unique identifier of the user.
     * @param array  $message The message data containing notification details.
     * @param string $vin     The vehicle identification number.
     *
     * @return array The formatted notification data.
     */
    private function vehicleXfFormattedNotification(string $userId, array $message, string $vin): array
    {
        $notificationData = [];
        $notification = [
            "context" => [
                "brandMarketingName" => $message['data']['context']['brandMarketingName'] ?? "",
                "firstName" => $message['data']['context']['firstName'] ?? '',
                "model" => $message['data']['context']['model'] ?? "",
                "nickname" => $message['data']['context']['nickname'] ?? "",
            ],
            "in-app" => [
                "criticality" => $message['criticality'],
                "body" => $message['notification']['body'] ?? '',
                "subtitle" => $message['notification']['subtitle'] ?? '',
                "title" => $message['notification']['title'] ?? '',
            ],
            "data" => [
                "notificationId" => $message['data']['serviceData']['Data']['notificationId'] ?? '',
                "userId" => $userId,
            ],
            "click_action" => $message['apiPushConfig']['staticConfig']['android']['notification']['click_action'] ?? ""
        ];

        $notificationData['id'] = Uuid::v4();
        $notificationData['notification'] = $notification;
        $notificationData['eventName'] = $message['data']['serviceData']['Data']['eventName'] ?? '';
        $notificationData['timestamp'] = $message['data']['serviceData']['Timestamp'] ?? null;
        $notificationData['vin'] = $vin;

        return $notificationData;
    }

    /**
     * Check if the exception is a MongoDB connection error
     */
    private function isMongoConnectionError(\Exception $e): bool
    {
        return str_contains($e->getMessage(), 'Connection') ||
               str_contains($e->getMessage(), 'connection') ||
               str_contains($e->getMessage(), 'timeout') ||
               str_contains($e->getMessage(), 'network');
    }
}
