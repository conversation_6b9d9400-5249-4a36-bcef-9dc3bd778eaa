<?php

namespace App\Manager;

use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Component\HttpFoundation\Response;

use App\Helper\ErrorResponse;
use App\Helper\ResponseArrayFormat;
use App\Helper\SuccessResponse;
use App\Trait\LoggerTrait;
use App\Model\FcmTokenRegisterModel;
use App\Service\RegisterOdmService;
use App\Trait\ValidationResponseTrait;


class RegisterManager
{
    use LoggerTrait;
    use ValidationResponseTrait;

    public function __construct(
        private ValidatorInterface $validator,
        private RegisterOdmService $registerService
    ) {
    }
    public function registerFcmToken(FcmTokenRegisterModel $fcmTokenRegisterModel, string $userId): ResponseArrayFormat
    {
        try {
            $violations = $this->validator->validate($fcmTokenRegisterModel, null, ['fcmTokenRegister']);
            if (0 < count($violations)) {
                $violationsDescription = $this->getValidationMessages($violations);
                $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                    'model' => $fcmTokenRegisterModel,
                    'desc' => $violationsDescription,
                ]);

                return $this->getValidationErrorResponse($violationsDescription);
            }
            $response = $this->registerService->registerFcmToken($fcmTokenRegisterModel, $userId);
            if (Response::HTTP_OK == $response->getCode())
            {
                return new SuccessResponse(['message' => "fcm token registered successfully"], 200);
            }
            return new ErrorResponse($response->getData(), $response->getCode());
        } catch (\Throwable $e) {
            $this->logger->error(__METHOD__ .' Catching Exception '.$e->getMessage(), [
                'model' => $fcmTokenRegisterModel,
                'exception' => $e,
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }

    public function registerSessionId(FcmTokenRegisterModel $fcmTokenRegisterModel, string $userId, string $sessionId): ResponseArrayFormat
    {
        try {
            $violations = $this->validator->validate($fcmTokenRegisterModel, null, ['fcmTokenRegister']);
            if (0 < count($violations)) {
                $violationsDescription = $this->getValidationMessages($violations);
                $this->logger->error(__METHOD__ . ' Validation fails for incoming request', [
                    'model' => $fcmTokenRegisterModel,
                    'desc' => $violationsDescription,
                ]);

                return $this->getValidationErrorResponse($violationsDescription);
            }
            $response = $this->registerService->registerSessionId($fcmTokenRegisterModel, $userId, $sessionId);
            if (Response::HTTP_OK == $response->getCode())
            {
                return new SuccessResponse(['message' => "sessionId registered successfully"], 200);
            }
            return new ErrorResponse($response->getData(), $response->getCode());
        } catch (\Throwable $e) {
            $this->logger->error(__METHOD__ .' Catching Exception '.$e->getMessage(), [
                'model' => $fcmTokenRegisterModel,
                'exception' => $e,
            ]);

            return new ErrorResponse($e->getMessage(), $e->getCode());
        }
    }
}