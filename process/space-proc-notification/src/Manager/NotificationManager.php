<?php

namespace App\Manager;

use App\Helper\{ErrorResponse, ResponseArrayFormat, SuccessResponse};
use App\Trait\LoggerTrait;
use App\Service\NotificationOdmService;
use App\Service\UserDataOdmService;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\Exception\UnrecoverableMessageHandlingException;

use Exception;

class NotificationManager
{

    use LoggerTrait;

    public function __construct(
        private NotificationOdmService $notificationService,
        private UserDataOdmService $userDataService
    ) {
    }

    /**
     * Get Notifications for a user
     * @param string $userId
     * @param array $data
     * @param string $vin
     * @return array
     */
    public function getVehicleNotifications(string $userId, array $data, string $vin): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ':: ' ." for vin " . $vin);
            $usersResponse = $this->userDataService->getUserByUserId($userId);
            $users = json_decode($usersResponse->getData(), true)['documents'] ?? [];

            if (empty($users)) {
                $this->logger->error('No users found', ['userId' => $userId]);
                throw new UnrecoverableMessageHandlingException('No users found');
            }
    
            $response = $this->notificationService->getVehicleNotifications($userId, $vin, $data);
            
            if ($response->getCode() === Response::HTTP_OK) {
                $decodedData = json_decode($response->getData(), true);
                $documents = $decodedData['documents'] ?? [];
                $returnData = [
                    "userId" => $userId,
                    "startTS" => (int) $data['since'],
                    "endTS" => (int) $data['till'],
                    "notifications" => reset($documents)
                ];

                $noData = ["userId" => $userId, "startTS" => $data['since'], "endTS" => $data['till'], "notifications" => [["count" => 0, "items" => []]]];

                return [
                    'content' => !empty($returnData) ? $returnData : $noData,
                    'code' => Response::HTTP_OK,
                ];
            }
    
            // Handle error responses
            $result = json_decode($response->getData(), true);
            $errorMessage = $result['error']['message'] ?? 'Unknown error occurred';
    
            return [
                'content' => ['error' => ['message' => $errorMessage]],
                'code' => $response->getCode(),
            ];
        } catch (UnrecoverableMessageHandlingException $e) {
            return [
                'content' => ['error' => ['message' => $e->getMessage()]],
                'code' => Response::HTTP_BAD_REQUEST,
            ];
        } catch (Exception $e) {
            $this->logger->error("Caught Exception in NotificationManager::getNotifications: " . $e->getMessage());
            return [
                'content' => ['error' => ['message' => $e->getMessage()]],
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
            ];
        }
    }

    /**
     * Get Notifications for a user
     * @param string $userId
     * @param array $data
     * @return array
     */
    public function getUserVehicleNotifications(string $userId, array $data): array
    {
        try {
            $this->logger->info(__CLASS__ . '::' . __METHOD__ . ':: ' ." for userId " . $userId);
            $usersResponse = $this->userDataService->getUserByUserId($userId);
            $users = json_decode($usersResponse->getData(), true)['documents'] ?? [];

            if (empty($users)) {
                $this->logger->error('No users found', ['userId' => $userId]);
                throw new UnrecoverableMessageHandlingException('No users found');
            }
    
            $response = $this->notificationService->getUserVehicleNotifications($userId, $data);
            
            if ($response->getCode() === Response::HTTP_OK) {
                $decodedData = json_decode($response->getData(), true);
                $documents = $decodedData['documents'] ?? [];
                $returnData = [
                    "userId" => $userId,
                    "startTS" => (int) $data['since'],
                    "endTS" => (int) $data['till'],
                    "notifications" => $documents
                ];

                return [
                    'content' => $returnData,
                    'code' => Response::HTTP_OK,
                ];
            }
    
            // Handle error responses
            $result = json_decode($response->getData(), true);
            $errorMessage = $result['error']['message'] ?? 'Unknown error occurred';
    
            return [
                'content' => ['error' => ['message' => $errorMessage]],
                'code' => $response->getCode(),
            ];
        } catch (UnrecoverableMessageHandlingException $e) {
            return [
                'content' => ['error' => ['message' => $e->getMessage()]],
                'code' => Response::HTTP_BAD_REQUEST,
            ];
        } catch (Exception $e) {
            $this->logger->error("Caught Exception in NotificationManager::getNotifications: " . $e->getMessage());
            return [
                'content' => ['error' => ['message' => $e->getMessage()]],
                'code' => Response::HTTP_INTERNAL_SERVER_ERROR,
            ];
        }
    }
}