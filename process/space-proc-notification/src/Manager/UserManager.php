<?php

namespace App\Manager;

use App\Trait\LoggerTrait;
use App\Service\UserDataOdmService;

class UserManager
{
    use LoggerTrait;
    public function __construct(
        private UserDataOdmService $userDataService
    ) {
    }

    public function getUsersBySessionId(?string $userId, ?string $vin, ?string $sessionId)
    {
        $this->logger->info('Getting users', ['userId' => $userId, 'vin' => $vin, 'sessionId' => $sessionId]);
        $users = $this->userDataService->getUsersBySessionId($userId, $vin, $sessionId);
        return $users;
    }

    public function getUserByUserId($userId)
    {
        $this->logger->info('Getting user', ['userId' => $userId]);
        $user = $this->userDataService->getUserByUserId($userId);
        return $user;
    }
}