doctrine_mongodb:
    connections:
        default:
            server: '%env(MONGODB_URL)%'
            options: {}
    default_database: '%env(MONGODB_DB)%'
    document_managers:
        default:
            auto_mapping: true
            mappings:
                SpaceMongoDocuments:
                    type: attribute
                    dir: '%kernel.project_dir%/../../space-mongo-documents/src/Document'
                    prefix: 'Space\MongoDocuments\Document'
                    alias: SpaceMongoDocuments
