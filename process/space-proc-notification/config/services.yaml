# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:
    # MongoDB ODM
    mongodb.url: "%env(MONGODB_URL)%"
    mongodb.db: "%env(MONGODB_DB)%"
    env(MONGODB_URL): ''
    env(MONGODB_DB): ''
    
services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'
    
    App\Connector\SystemFirebaseConnector:
        arguments:
            $url: "%env(MS_SYS_FIREBASE_URL)%"
    
    # Space MongoDB Documents Service
    Space\MongoDocuments\Service\MongoDBService:
        arguments:
            $documentManager: '@doctrine_mongodb.odm.document_manager'
            $logger: '@logger'

    # Space MongoDB Documents Repositories
    Space\MongoDocuments\Repository\UserDataRepository:
        factory: ['@doctrine_mongodb.odm.document_manager', 'getRepository']
        arguments: ['Space\MongoDocuments\Document\UserData']

    Space\MongoDocuments\Repository\NotificationRepository:
        factory: ['@doctrine_mongodb.odm.document_manager', 'getRepository']
        arguments: ['Space\MongoDocuments\Document\Notification']

    # New ODM Services
    App\Service\NotificationOdmService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'

    App\Service\UserDataOdmService:
        arguments:
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'

    App\Service\RegisterOdmService:
        arguments:
            $userDataService: '@App\Service\UserDataOdmService'
            $mongoDBService: '@Space\MongoDocuments\Service\MongoDBService'
            $serializer: '@serializer'

    Symfony\Component\Messenger\Transport\TransportInterface:
        tags: [messenger.space_vehicle_notification_sqs_queue,
                messenger.space_user_notification_sqs_queue
                ]

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones
