<?php

namespace App\Tests\Service;

use App\Helper\WSResponse;
use App\Service\NotificationOdmService;
use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Psr\Log\LoggerInterface;
use Space\MongoDocuments\Document\Notification;
use Space\MongoDocuments\Repository\NotificationRepository;
use Space\MongoDocuments\Service\MongoDBService;
use Symfony\Component\HttpFoundation\Response;
use Doctrine\ODM\MongoDB\DocumentManager;

/**
 * Tests for the NotificationOdmService class
 *
 * This test suite covers all scenarios for the NotificationOdmService:
 * - Constructor and initialization
 * - Vehicle notifications retrieval
 * - User vehicle notifications retrieval
 * - Notification saving
 * - Error handling and logging
 * - Response formatting
 */
class NotificationOdmServiceTest extends TestCase
{
    private NotificationOdmService $service;
    private MongoDBService|MockObject $mongoDBService;
    private DocumentManager|MockObject $documentManager;
    private NotificationRepository|MockObject $repository;
    private LoggerInterface|MockObject $logger;

    protected function setUp(): void
    {
        $this->mongoDBService = $this->createMock(MongoDBService::class);
        $this->documentManager = $this->createMock(DocumentManager::class);
        $this->repository = $this->createMock(NotificationRepository::class);
        $this->logger = $this->createMock(LoggerInterface::class);

        $this->service = new NotificationOdmService($this->mongoDBService);

        // Use reflection to inject the logger
        $reflection = new \ReflectionClass($this->service);
        $loggerProperty = $reflection->getProperty('logger');
        $loggerProperty->setAccessible(true);
        $loggerProperty->setValue($this->service, $this->logger);
    }

    public function testConstructor(): void
    {
        $this->assertInstanceOf(NotificationOdmService::class, $this->service);
    }

    public function testGetVehicleNotificationsSuccess(): void
    {
        $userId = 'test-user-123';
        $vin = 'test-vin-456';
        $data = ['since' => 1640995200, 'till' => 1641081600];

        $mockResult = [
            ['count' => 2, 'items' => [
                ['id' => 'notif1', 'message' => 'Test notification 1'],
                ['id' => 'notif2', 'message' => 'Test notification 2']
            ]]
        ];

        $this->mongoDBService->expects($this->once())
            ->method('getDocumentManager')
            ->willReturn($this->documentManager);

        $this->documentManager->expects($this->once())
            ->method('getRepository')
            ->with(Notification::class)
            ->willReturn($this->repository);

        $this->repository->expects($this->once())
            ->method('getVehicleNotifications')
            ->with($userId, $vin, $data)
            ->willReturn($mockResult);

        $this->logger->expects($this->exactly(2))
            ->method('info');

        $response = $this->service->getVehicleNotifications($userId, $vin, $data);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('documents', $responseData);
        $this->assertEquals($mockResult, $responseData['documents']);
    }

    public function testGetVehicleNotificationsWithException(): void
    {
        $userId = 'test-user-123';
        $vin = 'test-vin-456';
        $data = ['since' => 1640995200, 'till' => 1641081600];

        $this->mongoDBService->expects($this->once())
            ->method('getDocumentManager')
            ->willThrowException(new \Exception('Database connection failed'));

        $this->logger->expects($this->once())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error');

        $response = $this->service->getVehicleNotifications($userId, $vin, $data);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Database connection failed', $responseData['error']['message']);
    }

    public function testGetUserVehicleNotificationsSuccess(): void
    {
        $userId = 'test-user-123';
        $data = ['since' => 1640995200, 'till' => 1641081600];

        $mockResult = [
            ['count' => 1, 'items' => [
                ['id' => 'notif1', 'message' => 'User notification 1']
            ]]
        ];

        $this->mongoDBService->expects($this->once())
            ->method('getDocumentManager')
            ->willReturn($this->documentManager);

        $this->documentManager->expects($this->once())
            ->method('getRepository')
            ->with(Notification::class)
            ->willReturn($this->repository);

        $this->repository->expects($this->once())
            ->method('getUserVehicleNotifications')
            ->with($userId, $data)
            ->willReturn($mockResult);

        $this->logger->expects($this->exactly(2))
            ->method('info');

        $response = $this->service->getUserVehicleNotifications($userId, $data);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('documents', $responseData);
        $this->assertEquals($mockResult, $responseData['documents']);
    }

    public function testGetUserVehicleNotificationsWithException(): void
    {
        $userId = 'test-user-123';
        $data = ['since' => 1640995200, 'till' => 1641081600];

        $this->mongoDBService->expects($this->once())
            ->method('getDocumentManager')
            ->willThrowException(new \Exception('Repository error'));

        $this->logger->expects($this->once())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error');

        $response = $this->service->getUserVehicleNotifications($userId, $data);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Repository error', $responseData['error']['message']);
    }

    public function testSaveNotificationSuccess(): void
    {
        $notificationData = [
            'userId' => 'test-user-123',
            'message' => 'Test notification',
            'type' => 'vehicle'
        ];

        $mockNotification = $this->createMock(Notification::class);

        $this->mongoDBService->expects($this->once())
            ->method('save')
            ->with($this->isInstanceOf(Notification::class))
            ->willReturn($mockNotification);

        $this->logger->expects($this->exactly(2))
            ->method('info');

        $response = $this->service->saveNotification($notificationData);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_OK, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('success', $responseData);
        $this->assertTrue($responseData['success']);
    }

    public function testSaveNotificationWithException(): void
    {
        $notificationData = [
            'userId' => 'test-user-123',
            'message' => 'Test notification',
            'type' => 'vehicle'
        ];

        $this->mongoDBService->expects($this->once())
            ->method('save')
            ->willThrowException(new \Exception('Save failed'));

        $this->logger->expects($this->once())
            ->method('info');

        $this->logger->expects($this->once())
            ->method('error');

        $response = $this->service->saveNotification($notificationData);

        $this->assertInstanceOf(WSResponse::class, $response);
        $this->assertEquals(Response::HTTP_INTERNAL_SERVER_ERROR, $response->getCode());

        $responseData = json_decode($response->getData(), true);
        $this->assertArrayHasKey('error', $responseData);
        $this->assertEquals('Save failed', $responseData['error']['message']);
    }

    public function testFormatXfNotificationFormatWithValidData(): void
    {
        $data = [
            'count' => 2,
            'items' => [
                ['id' => 'notif1', 'message' => 'Test 1'],
                ['id' => 'notif2', 'message' => 'Test 2']
            ]
        ];

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('formatXfNotificationFormat');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->service, [$data]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('items', $result);
        $this->assertEquals(2, $result['count']);
        $this->assertCount(2, $result['items']);
    }

    public function testFormatXfNotificationFormatWithEmptyData(): void
    {
        $data = [];

        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('formatXfNotificationFormat');
        $method->setAccessible(true);

        $result = $method->invokeArgs($this->service, [$data]);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('count', $result);
        $this->assertArrayHasKey('items', $result);
        $this->assertEquals(0, $result['count']);
        $this->assertEmpty($result['items']);
    }

    public function testIsMongoConnectionError(): void
    {
        $reflection = new \ReflectionClass($this->service);
        $method = $reflection->getMethod('isMongoConnectionError');
        $method->setAccessible(true);

        // Test connection error
        $connectionException = new \Exception('Connection timeout');
        $this->assertTrue($method->invokeArgs($this->service, [$connectionException]));

        // Test network error
        $networkException = new \Exception('Network error occurred');
        $this->assertTrue($method->invokeArgs($this->service, [$networkException]));

        // Test other error
        $otherException = new \Exception('Some other error');
        $this->assertFalse($method->invokeArgs($this->service, [$otherException]));
    }
}
